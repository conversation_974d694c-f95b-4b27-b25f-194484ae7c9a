﻿namespace 工具箱
{
    partial class Form1
    {
        /// <summary>
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows 窗体设计器生成的代码

        /// <summary>
        /// 设计器支持所需的方法 - 不要修改
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            System.Windows.Forms.TreeNode treeNode1 = new System.Windows.Forms.TreeNode("📋 显示全部");
            System.Windows.Forms.TreeNode treeNode2 = new System.Windows.Forms.TreeNode("🧠 DeepSeek");
            System.Windows.Forms.TreeNode treeNode3 = new System.Windows.Forms.TreeNode("💬 ChatGPT");
            System.Windows.Forms.TreeNode treeNode4 = new System.Windows.Forms.TreeNode("📊 PPT助手");
            System.Windows.Forms.TreeNode treeNode5 = new System.Windows.Forms.TreeNode("🦝 小浣熊");
            System.Windows.Forms.TreeNode treeNode6 = new System.Windows.Forms.TreeNode("👤 数字人生成");
            System.Windows.Forms.TreeNode treeNode7 = new System.Windows.Forms.TreeNode("🎨 文生图");
            System.Windows.Forms.TreeNode treeNode8 = new System.Windows.Forms.TreeNode("🎬 文生视频");
            System.Windows.Forms.TreeNode treeNode9 = new System.Windows.Forms.TreeNode("🎵 文生声音");
            System.Windows.Forms.TreeNode treeNode10 = new System.Windows.Forms.TreeNode("📄 文生PPT");
            System.Windows.Forms.TreeNode treeNode11 = new System.Windows.Forms.TreeNode("🤖 AI工具", new System.Windows.Forms.TreeNode[] {
            treeNode2,
            treeNode3,
            treeNode4,
            treeNode5,
            treeNode6,
            treeNode7,
            treeNode8,
            treeNode9,
            treeNode10});
            System.Windows.Forms.TreeNode treeNode12 = new System.Windows.Forms.TreeNode("🧹 释放系统内存");
            System.Windows.Forms.TreeNode treeNode13 = new System.Windows.Forms.TreeNode("🔐 修改计算机密码");
            System.Windows.Forms.TreeNode treeNode14 = new System.Windows.Forms.TreeNode("🌐 IP信息查看");
            System.Windows.Forms.TreeNode treeNode15 = new System.Windows.Forms.TreeNode("🔑 激活工具");
            System.Windows.Forms.TreeNode treeNode16 = new System.Windows.Forms.TreeNode("⚙️ 系统工具", new System.Windows.Forms.TreeNode[] {
            treeNode12,
            treeNode13,
            treeNode14,
            treeNode15});
            System.Windows.Forms.TreeNode treeNode17 = new System.Windows.Forms.TreeNode("🏢 NVT-OA");
            System.Windows.Forms.TreeNode treeNode18 = new System.Windows.Forms.TreeNode("🔍 百度");
            System.Windows.Forms.TreeNode treeNode19 = new System.Windows.Forms.TreeNode("🔄 文件转换");
            System.Windows.Forms.TreeNode treeNode20 = new System.Windows.Forms.TreeNode("🌍 网络工具", new System.Windows.Forms.TreeNode[] {
            treeNode17,
            treeNode18,
            treeNode19});
            System.Windows.Forms.TreeNode treeNode21 = new System.Windows.Forms.TreeNode("📁 搜索文件");
            System.Windows.Forms.TreeNode treeNode22 = new System.Windows.Forms.TreeNode("🗺️ 思维导图");
            System.Windows.Forms.TreeNode treeNode23 = new System.Windows.Forms.TreeNode("📈 FineBI商业智能");
            System.Windows.Forms.TreeNode treeNode24 = new System.Windows.Forms.TreeNode("🛠️ 实用工具", new System.Windows.Forms.TreeNode[] {
            treeNode21,
            treeNode22,
            treeNode23});
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(Form1));
            this.selectFunction = new AntdUI.Select();
            this.sidebarPanel = new System.Windows.Forms.Panel();
            this.sidebarLabel = new System.Windows.Forms.Label();
            this.toolsTreeView = new System.Windows.Forms.TreeView();
            this.scrollPanel = new System.Windows.Forms.Panel();
            this.tableLayoutPanel1 = new System.Windows.Forms.TableLayoutPanel();
            this.btnReleaseMemory = new AntdUI.Button();
            this.btnChangePassword = new AntdUI.Button();
            this.btnNvtOa = new AntdUI.Button();
            this.btnDeepSeek = new AntdUI.Button();
            this.btnSearchFiles = new AntdUI.Button();
            this.btnPrivacyLock = new AntdUI.Button();
            this.btnMindMap = new AntdUI.Button();
            this.btnChatGPT = new AntdUI.Button();
            this.btnFileConvert = new AntdUI.Button();
            this.btnActivationTool = new AntdUI.Button();
            this.btnBaidu = new AntdUI.Button();
            this.btnIPInfo = new AntdUI.Button();
            this.btnPPTAssistant = new AntdUI.Button();
            this.btnXiaoHuanXiong = new AntdUI.Button();
            this.btnDigitalHuman = new AntdUI.Button();
            this.btnTextToImage = new AntdUI.Button();
            this.btnTextToVideo = new AntdUI.Button();
            this.btnTextToVoice = new AntdUI.Button();
            this.btnTextToPPT = new AntdUI.Button();
            this.alertNotification = new AntdUI.Alert();
            this.label1 = new AntdUI.Label();
            this.skinEngine1 = new Sunisoft.IrisSkin.SkinEngine();
            this.sidebarPanel.SuspendLayout();
            this.scrollPanel.SuspendLayout();
            this.tableLayoutPanel1.SuspendLayout();
            this.SuspendLayout();
            // 
            // selectFunction
            // 
            this.selectFunction.AllowClear = true;
            this.selectFunction.BorderActive = System.Drawing.Color.FromArgb(((int)(((byte)(76)))), ((int)(((byte)(175)))), ((int)(((byte)(80)))));
            this.selectFunction.BorderHover = System.Drawing.Color.FromArgb(((int)(((byte)(233)))), ((int)(((byte)(30)))), ((int)(((byte)(99)))));
            this.selectFunction.ClickSwitchDropdown = false;
            this.selectFunction.Location = new System.Drawing.Point(10, 13);
            this.selectFunction.MaxCount = 0;
            this.selectFunction.Name = "selectFunction";
            this.selectFunction.PlaceholderText = "搜索功能...";
            this.selectFunction.SelectionColor = System.Drawing.Color.Empty;
            this.selectFunction.Size = new System.Drawing.Size(142, 35);
            this.selectFunction.TabIndex = 15;
            this.selectFunction.TabStop = false;
            // 
            // sidebarPanel
            // 
            this.sidebarPanel.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left)));
            this.sidebarPanel.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(248)))), ((int)(((byte)(249)))), ((int)(((byte)(250)))));
            this.sidebarPanel.Controls.Add(this.sidebarLabel);
            this.sidebarPanel.Controls.Add(this.toolsTreeView);
            this.sidebarPanel.Location = new System.Drawing.Point(10, 55);
            this.sidebarPanel.Name = "sidebarPanel";
            this.sidebarPanel.Size = new System.Drawing.Size(181, 369);
            this.sidebarPanel.TabIndex = 20;
            // 
            // sidebarLabel
            // 
            this.sidebarLabel.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.sidebarLabel.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(52)))), ((int)(((byte)(73)))), ((int)(((byte)(94)))));
            this.sidebarLabel.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle;
            this.sidebarLabel.Font = new System.Drawing.Font("微软雅黑", 11F, System.Drawing.FontStyle.Bold);
            this.sidebarLabel.ForeColor = System.Drawing.Color.White;
            this.sidebarLabel.Location = new System.Drawing.Point(0, 0);
            this.sidebarLabel.Name = "sidebarLabel";
            this.sidebarLabel.Padding = new System.Windows.Forms.Padding(10, 0, 0, 0);
            this.sidebarLabel.Size = new System.Drawing.Size(178, 37);
            this.sidebarLabel.TabIndex = 1;
            this.sidebarLabel.Text = "🔧 工具分类";
            this.sidebarLabel.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
            // 
            // toolsTreeView
            // 
            this.toolsTreeView.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.toolsTreeView.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(248)))), ((int)(((byte)(249)))), ((int)(((byte)(250)))));
            this.toolsTreeView.BorderStyle = System.Windows.Forms.BorderStyle.None;
            this.toolsTreeView.Cursor = System.Windows.Forms.Cursors.Default;
            this.toolsTreeView.Font = new System.Drawing.Font("微软雅黑", 9.75F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.toolsTreeView.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(52)))), ((int)(((byte)(73)))), ((int)(((byte)(94)))));
            this.toolsTreeView.FullRowSelect = true;
            this.toolsTreeView.Indent = 20;
            this.toolsTreeView.ItemHeight = 20;
            this.toolsTreeView.Location = new System.Drawing.Point(0, 35);
            this.toolsTreeView.Name = "toolsTreeView";
            treeNode1.Name = "";
            treeNode1.Text = "📋 显示全部";
            treeNode2.Name = "";
            treeNode2.Text = "🧠 DeepSeek";
            treeNode3.Name = "";
            treeNode3.Text = "💬 ChatGPT";
            treeNode4.Name = "";
            treeNode4.Text = "📊 PPT助手";
            treeNode5.Name = "";
            treeNode5.Text = "🦝 小浣熊";
            treeNode6.Name = "";
            treeNode6.Text = "👤 数字人生成";
            treeNode7.Name = "";
            treeNode7.Text = "🎨 文生图";
            treeNode8.Name = "";
            treeNode8.Text = "🎬 文生视频";
            treeNode9.Name = "";
            treeNode9.Text = "🎵 文生声音";
            treeNode10.Name = "";
            treeNode10.Text = "📄 文生PPT";
            treeNode11.Name = "";
            treeNode11.Text = "🤖 AI工具";
            treeNode12.Name = "";
            treeNode12.Text = "🧹 释放系统内存";
            treeNode13.Name = "";
            treeNode13.Text = "🔐 修改计算机密码";
            treeNode14.Name = "";
            treeNode14.Text = "🌐 IP信息查看";
            treeNode15.Name = "";
            treeNode15.Text = "🔑 激活工具";
            treeNode16.Name = "";
            treeNode16.Text = "⚙️ 系统工具";
            treeNode17.Name = "";
            treeNode17.Text = "🏢 NVT-OA";
            treeNode18.Name = "";
            treeNode18.Text = "🔍 百度";
            treeNode19.Name = "";
            treeNode19.Text = "🔄 文件转换";
            treeNode20.Name = "";
            treeNode20.Text = "🌍 网络工具";
            treeNode21.Name = "";
            treeNode21.Text = "📁 搜索文件";
            treeNode22.Name = "";
            treeNode22.Text = "🗺️ 思维导图";
            treeNode23.Name = "";
            treeNode23.Text = "📈 FineBI商业智能";
            treeNode24.Name = "";
            treeNode24.Text = "🛠️ 实用工具";
            this.toolsTreeView.Nodes.AddRange(new System.Windows.Forms.TreeNode[] {
            treeNode1,
            treeNode11,
            treeNode16,
            treeNode20,
            treeNode24});
            this.toolsTreeView.Size = new System.Drawing.Size(178, 332);
            this.toolsTreeView.TabIndex = 0;
            // 
            // scrollPanel
            // 
            this.scrollPanel.AutoScroll = true;
            this.scrollPanel.Controls.Add(this.tableLayoutPanel1);
            this.scrollPanel.Location = new System.Drawing.Point(194, 55);
            this.scrollPanel.Name = "scrollPanel";
            this.scrollPanel.Size = new System.Drawing.Size(537, 369);
            this.scrollPanel.TabIndex = 19;
            // 
            // tableLayoutPanel1
            // 
            this.tableLayoutPanel1.ColumnCount = 4;
            this.tableLayoutPanel1.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 25F));
            this.tableLayoutPanel1.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 25F));
            this.tableLayoutPanel1.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 25F));
            this.tableLayoutPanel1.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 25F));
            this.tableLayoutPanel1.Controls.Add(this.btnReleaseMemory, 0, 0);
            this.tableLayoutPanel1.Controls.Add(this.btnChangePassword, 1, 0);
            this.tableLayoutPanel1.Controls.Add(this.btnNvtOa, 2, 0);
            this.tableLayoutPanel1.Controls.Add(this.btnDeepSeek, 3, 0);
            this.tableLayoutPanel1.Controls.Add(this.btnSearchFiles, 0, 1);
            this.tableLayoutPanel1.Controls.Add(this.btnPrivacyLock, 1, 1);
            this.tableLayoutPanel1.Controls.Add(this.btnMindMap, 2, 1);
            this.tableLayoutPanel1.Controls.Add(this.btnChatGPT, 3, 1);
            this.tableLayoutPanel1.Controls.Add(this.btnFileConvert, 0, 2);
            this.tableLayoutPanel1.Controls.Add(this.btnActivationTool, 1, 2);
            this.tableLayoutPanel1.Controls.Add(this.btnBaidu, 2, 2);
            this.tableLayoutPanel1.Controls.Add(this.btnIPInfo, 3, 2);
            this.tableLayoutPanel1.Controls.Add(this.btnPPTAssistant, 0, 3);
            this.tableLayoutPanel1.Controls.Add(this.btnXiaoHuanXiong, 1, 3);
            this.tableLayoutPanel1.Controls.Add(this.btnDigitalHuman, 2, 3);
            this.tableLayoutPanel1.Controls.Add(this.btnTextToImage, 3, 3);
            this.tableLayoutPanel1.Controls.Add(this.btnTextToVideo, 0, 4);
            this.tableLayoutPanel1.Controls.Add(this.btnTextToVoice, 1, 4);
            this.tableLayoutPanel1.Controls.Add(this.btnTextToPPT, 2, 4);
            this.tableLayoutPanel1.Font = new System.Drawing.Font("宋体", 11.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.tableLayoutPanel1.Location = new System.Drawing.Point(0, 0);
            this.tableLayoutPanel1.Name = "tableLayoutPanel1";
            this.tableLayoutPanel1.RowCount = 6;
            this.tableLayoutPanel1.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 100F));
            this.tableLayoutPanel1.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 100F));
            this.tableLayoutPanel1.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 100F));
            this.tableLayoutPanel1.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 100F));
            this.tableLayoutPanel1.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 100F));
            this.tableLayoutPanel1.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 100F));
            this.tableLayoutPanel1.Size = new System.Drawing.Size(517, 403);
            this.tableLayoutPanel1.TabIndex = 1;
            // 
            // btnReleaseMemory
            // 
            this.btnReleaseMemory.Dock = System.Windows.Forms.DockStyle.Fill;
            this.btnReleaseMemory.Location = new System.Drawing.Point(3, 3);
            this.btnReleaseMemory.Name = "btnReleaseMemory";
            this.btnReleaseMemory.Size = new System.Drawing.Size(123, 94);
            this.btnReleaseMemory.TabIndex = 0;
            this.btnReleaseMemory.Text = "释放系统内存";
            this.btnReleaseMemory.Toggle = true;
            this.btnReleaseMemory.ToggleBackHover = System.Drawing.Color.FromArgb(((int)(((byte)(22)))), ((int)(((byte)(119)))), ((int)(((byte)(255)))));
            // 
            // btnChangePassword
            // 
            this.btnChangePassword.Dock = System.Windows.Forms.DockStyle.Fill;
            this.btnChangePassword.Location = new System.Drawing.Point(132, 3);
            this.btnChangePassword.Name = "btnChangePassword";
            this.btnChangePassword.Size = new System.Drawing.Size(123, 94);
            this.btnChangePassword.TabIndex = 1;
            this.btnChangePassword.Text = "修改计算机密码";
            this.btnChangePassword.Toggle = true;
            this.btnChangePassword.ToggleBackHover = System.Drawing.Color.FromArgb(((int)(((byte)(22)))), ((int)(((byte)(119)))), ((int)(((byte)(255)))));
            // 
            // btnNvtOa
            // 
            this.btnNvtOa.DefaultBorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(22)))), ((int)(((byte)(119)))), ((int)(((byte)(255)))));
            this.btnNvtOa.Dock = System.Windows.Forms.DockStyle.Fill;
            this.btnNvtOa.Location = new System.Drawing.Point(261, 3);
            this.btnNvtOa.Name = "btnNvtOa";
            this.btnNvtOa.Size = new System.Drawing.Size(123, 94);
            this.btnNvtOa.TabIndex = 2;
            this.btnNvtOa.Text = "NVT-OA";
            this.btnNvtOa.Toggle = true;
            this.btnNvtOa.ToggleBackHover = System.Drawing.Color.FromArgb(((int)(((byte)(22)))), ((int)(((byte)(119)))), ((int)(((byte)(255)))));
            // 
            // btnDeepSeek
            // 
            this.btnDeepSeek.Dock = System.Windows.Forms.DockStyle.Fill;
            this.btnDeepSeek.IsLink = true;
            this.btnDeepSeek.Location = new System.Drawing.Point(390, 3);
            this.btnDeepSeek.Name = "btnDeepSeek";
            this.btnDeepSeek.Size = new System.Drawing.Size(124, 94);
            this.btnDeepSeek.TabIndex = 12;
            this.btnDeepSeek.TabStop = false;
            this.btnDeepSeek.Text = "DeepSeek";
            this.btnDeepSeek.Toggle = true;
            this.btnDeepSeek.ToggleBackHover = System.Drawing.Color.FromArgb(((int)(((byte)(22)))), ((int)(((byte)(119)))), ((int)(((byte)(255)))));
            // 
            // btnSearchFiles
            // 
            this.btnSearchFiles.Dock = System.Windows.Forms.DockStyle.Fill;
            this.btnSearchFiles.Location = new System.Drawing.Point(3, 103);
            this.btnSearchFiles.Name = "btnSearchFiles";
            this.btnSearchFiles.Size = new System.Drawing.Size(123, 94);
            this.btnSearchFiles.TabIndex = 4;
            this.btnSearchFiles.Text = "搜索电脑文件";
            this.btnSearchFiles.Toggle = true;
            this.btnSearchFiles.ToggleBackHover = System.Drawing.Color.FromArgb(((int)(((byte)(22)))), ((int)(((byte)(119)))), ((int)(((byte)(255)))));
            // 
            // btnPrivacyLock
            // 
            this.btnPrivacyLock.Dock = System.Windows.Forms.DockStyle.Fill;
            this.btnPrivacyLock.Location = new System.Drawing.Point(132, 103);
            this.btnPrivacyLock.Name = "btnPrivacyLock";
            this.btnPrivacyLock.Size = new System.Drawing.Size(123, 94);
            this.btnPrivacyLock.TabIndex = 5;
            this.btnPrivacyLock.Text = "FineBI商业智能";
            this.btnPrivacyLock.Toggle = true;
            this.btnPrivacyLock.ToggleBackHover = System.Drawing.Color.FromArgb(((int)(((byte)(22)))), ((int)(((byte)(119)))), ((int)(((byte)(255)))));
            // 
            // btnMindMap
            // 
            this.btnMindMap.Dock = System.Windows.Forms.DockStyle.Fill;
            this.btnMindMap.Location = new System.Drawing.Point(261, 103);
            this.btnMindMap.Name = "btnMindMap";
            this.btnMindMap.Size = new System.Drawing.Size(123, 94);
            this.btnMindMap.TabIndex = 13;
            this.btnMindMap.Text = "思维导图";
            this.btnMindMap.Toggle = true;
            this.btnMindMap.ToggleBackHover = System.Drawing.Color.FromArgb(((int)(((byte)(22)))), ((int)(((byte)(119)))), ((int)(((byte)(255)))));
            // 
            // btnChatGPT
            // 
            this.btnChatGPT.Dock = System.Windows.Forms.DockStyle.Fill;
            this.btnChatGPT.Location = new System.Drawing.Point(390, 103);
            this.btnChatGPT.Name = "btnChatGPT";
            this.btnChatGPT.Size = new System.Drawing.Size(124, 94);
            this.btnChatGPT.TabIndex = 7;
            this.btnChatGPT.Text = "ChatGPT";
            this.btnChatGPT.Toggle = true;
            this.btnChatGPT.ToggleBackHover = System.Drawing.Color.FromArgb(((int)(((byte)(22)))), ((int)(((byte)(119)))), ((int)(((byte)(255)))));
            // 
            // btnFileConvert
            // 
            this.btnFileConvert.Dock = System.Windows.Forms.DockStyle.Fill;
            this.btnFileConvert.Location = new System.Drawing.Point(3, 203);
            this.btnFileConvert.Name = "btnFileConvert";
            this.btnFileConvert.Size = new System.Drawing.Size(123, 94);
            this.btnFileConvert.TabIndex = 8;
            this.btnFileConvert.Text = "文件转换";
            this.btnFileConvert.Toggle = true;
            this.btnFileConvert.ToggleBackHover = System.Drawing.Color.FromArgb(((int)(((byte)(22)))), ((int)(((byte)(119)))), ((int)(((byte)(255)))));
            // 
            // btnActivationTool
            // 
            this.btnActivationTool.Dock = System.Windows.Forms.DockStyle.Fill;
            this.btnActivationTool.Location = new System.Drawing.Point(132, 203);
            this.btnActivationTool.Name = "btnActivationTool";
            this.btnActivationTool.Size = new System.Drawing.Size(123, 94);
            this.btnActivationTool.TabIndex = 16;
            this.btnActivationTool.Text = "激活工具";
            this.btnActivationTool.Toggle = true;
            this.btnActivationTool.ToggleBackHover = System.Drawing.Color.FromArgb(((int)(((byte)(22)))), ((int)(((byte)(119)))), ((int)(((byte)(255)))));
            // 
            // btnBaidu
            // 
            this.btnBaidu.Dock = System.Windows.Forms.DockStyle.Fill;
            this.btnBaidu.Location = new System.Drawing.Point(261, 203);
            this.btnBaidu.Name = "btnBaidu";
            this.btnBaidu.Size = new System.Drawing.Size(123, 94);
            this.btnBaidu.TabIndex = 10;
            this.btnBaidu.Text = "百度";
            this.btnBaidu.Toggle = true;
            this.btnBaidu.ToggleBackHover = System.Drawing.Color.FromArgb(((int)(((byte)(22)))), ((int)(((byte)(119)))), ((int)(((byte)(255)))));
            // 
            // btnIPInfo
            // 
            this.btnIPInfo.Dock = System.Windows.Forms.DockStyle.Fill;
            this.btnIPInfo.Location = new System.Drawing.Point(390, 203);
            this.btnIPInfo.Name = "btnIPInfo";
            this.btnIPInfo.Size = new System.Drawing.Size(124, 94);
            this.btnIPInfo.TabIndex = 17;
            this.btnIPInfo.Text = "IP信息查看";
            this.btnIPInfo.Toggle = true;
            this.btnIPInfo.ToggleBackHover = System.Drawing.Color.FromArgb(((int)(((byte)(22)))), ((int)(((byte)(119)))), ((int)(((byte)(255)))));
            // 
            // btnPPTAssistant
            // 
            this.btnPPTAssistant.Dock = System.Windows.Forms.DockStyle.Fill;
            this.btnPPTAssistant.Location = new System.Drawing.Point(3, 303);
            this.btnPPTAssistant.Name = "btnPPTAssistant";
            this.btnPPTAssistant.Size = new System.Drawing.Size(123, 94);
            this.btnPPTAssistant.TabIndex = 18;
            this.btnPPTAssistant.Text = "PPT助手";
            this.btnPPTAssistant.Toggle = true;
            this.btnPPTAssistant.ToggleBackHover = System.Drawing.Color.FromArgb(((int)(((byte)(22)))), ((int)(((byte)(119)))), ((int)(((byte)(255)))));
            // 
            // btnXiaoHuanXiong
            // 
            this.btnXiaoHuanXiong.Dock = System.Windows.Forms.DockStyle.Fill;
            this.btnXiaoHuanXiong.Location = new System.Drawing.Point(132, 303);
            this.btnXiaoHuanXiong.Name = "btnXiaoHuanXiong";
            this.btnXiaoHuanXiong.Size = new System.Drawing.Size(123, 94);
            this.btnXiaoHuanXiong.TabIndex = 19;
            this.btnXiaoHuanXiong.Text = "小浣熊";
            this.btnXiaoHuanXiong.Toggle = true;
            this.btnXiaoHuanXiong.ToggleBackHover = System.Drawing.Color.FromArgb(((int)(((byte)(22)))), ((int)(((byte)(119)))), ((int)(((byte)(255)))));
            // 
            // btnDigitalHuman
            // 
            this.btnDigitalHuman.Dock = System.Windows.Forms.DockStyle.Fill;
            this.btnDigitalHuman.Location = new System.Drawing.Point(261, 303);
            this.btnDigitalHuman.Name = "btnDigitalHuman";
            this.btnDigitalHuman.Size = new System.Drawing.Size(123, 94);
            this.btnDigitalHuman.TabIndex = 20;
            this.btnDigitalHuman.Text = "数字人生成";
            this.btnDigitalHuman.Toggle = true;
            this.btnDigitalHuman.ToggleBackHover = System.Drawing.Color.FromArgb(((int)(((byte)(22)))), ((int)(((byte)(119)))), ((int)(((byte)(255)))));
            // 
            // btnTextToImage
            // 
            this.btnTextToImage.Dock = System.Windows.Forms.DockStyle.Fill;
            this.btnTextToImage.Location = new System.Drawing.Point(390, 303);
            this.btnTextToImage.Name = "btnTextToImage";
            this.btnTextToImage.Size = new System.Drawing.Size(124, 94);
            this.btnTextToImage.TabIndex = 21;
            this.btnTextToImage.Text = "文生图";
            this.btnTextToImage.Toggle = true;
            this.btnTextToImage.ToggleBackHover = System.Drawing.Color.FromArgb(((int)(((byte)(22)))), ((int)(((byte)(119)))), ((int)(((byte)(255)))));
            // 
            // btnTextToVideo
            // 
            this.btnTextToVideo.Dock = System.Windows.Forms.DockStyle.Fill;
            this.btnTextToVideo.Location = new System.Drawing.Point(3, 403);
            this.btnTextToVideo.Name = "btnTextToVideo";
            this.btnTextToVideo.Size = new System.Drawing.Size(123, 94);
            this.btnTextToVideo.TabIndex = 22;
            this.btnTextToVideo.Text = "文生视频";
            this.btnTextToVideo.Toggle = true;
            this.btnTextToVideo.ToggleBackHover = System.Drawing.Color.FromArgb(((int)(((byte)(22)))), ((int)(((byte)(119)))), ((int)(((byte)(255)))));
            // 
            // btnTextToVoice
            // 
            this.btnTextToVoice.Dock = System.Windows.Forms.DockStyle.Fill;
            this.btnTextToVoice.Location = new System.Drawing.Point(132, 403);
            this.btnTextToVoice.Name = "btnTextToVoice";
            this.btnTextToVoice.Size = new System.Drawing.Size(123, 94);
            this.btnTextToVoice.TabIndex = 23;
            this.btnTextToVoice.Text = "文生声音";
            this.btnTextToVoice.Toggle = true;
            this.btnTextToVoice.ToggleBackHover = System.Drawing.Color.FromArgb(((int)(((byte)(22)))), ((int)(((byte)(119)))), ((int)(((byte)(255)))));
            // 
            // btnTextToPPT
            // 
            this.btnTextToPPT.Dock = System.Windows.Forms.DockStyle.Fill;
            this.btnTextToPPT.Location = new System.Drawing.Point(261, 403);
            this.btnTextToPPT.Name = "btnTextToPPT";
            this.btnTextToPPT.Size = new System.Drawing.Size(123, 94);
            this.btnTextToPPT.TabIndex = 24;
            this.btnTextToPPT.Text = "文生PPT";
            this.btnTextToPPT.Toggle = true;
            this.btnTextToPPT.ToggleBackHover = System.Drawing.Color.FromArgb(((int)(((byte)(22)))), ((int)(((byte)(119)))), ((int)(((byte)(255)))));
            // 
            // alertNotification
            // 
            this.alertNotification.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(92)))), ((int)(((byte)(184)))), ((int)(((byte)(92)))));
            this.alertNotification.Dock = System.Windows.Forms.DockStyle.Bottom;
            this.alertNotification.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.alertNotification.ForeColor = System.Drawing.Color.White;
            this.alertNotification.Icon = AntdUI.TType.Success;
            this.alertNotification.Location = new System.Drawing.Point(10, 430);
            this.alertNotification.Loop = true;
            this.alertNotification.LoopSpeed = 15;
            this.alertNotification.Name = "alertNotification";
            this.alertNotification.Size = new System.Drawing.Size(711, 44);
            this.alertNotification.TabIndex = 14;
            this.alertNotification.Text = "工具箱加载完成，所有功能已就绪！";
            // 
            // label1
            // 
            this.label1.Font = new System.Drawing.Font("幼圆", 15F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.label1.Location = new System.Drawing.Point(277, 13);
            this.label1.Name = "label1";
            this.label1.ShadowOpacity = 0F;
            this.label1.Size = new System.Drawing.Size(274, 30);
            this.label1.TabIndex = 16;
            this.label1.Text = "NVT东莞新能德科技有限公司";
            this.label1.TextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            // 
            // skinEngine1
            // 
            this.skinEngine1.@__DrawButtonFocusRectangle = true;
            this.skinEngine1.DisabledButtonTextColor = System.Drawing.Color.Gray;
            this.skinEngine1.DisabledMenuFontColor = System.Drawing.SystemColors.GrayText;
            this.skinEngine1.InactiveCaptionColor = System.Drawing.SystemColors.InactiveCaptionText;
            this.skinEngine1.SerialNumber = "";
            this.skinEngine1.SkinFile = null;
            // 
            // Form1
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 12F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(731, 484);
            this.Controls.Add(this.sidebarPanel);
            this.Controls.Add(this.label1);
            this.Controls.Add(this.alertNotification);
            this.Controls.Add(this.scrollPanel);
            this.Controls.Add(this.selectFunction);
            this.DoubleBuffered = true;
            this.FormBorderStyle = System.Windows.Forms.FormBorderStyle.FixedDialog;
            this.HelpButton = true;
            this.Icon = ((System.Drawing.Icon)(resources.GetObject("$this.Icon")));
            this.MaximizeBox = false;
            this.Name = "Form1";
            this.Padding = new System.Windows.Forms.Padding(10);
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen;
            this.Text = "桌面实用工具箱";
            this.sidebarPanel.ResumeLayout(false);
            this.scrollPanel.ResumeLayout(false);
            this.tableLayoutPanel1.ResumeLayout(false);
            this.ResumeLayout(false);

        }

        #endregion


        private AntdUI.Select selectFunction;
        private System.Windows.Forms.Panel sidebarPanel;
        private System.Windows.Forms.TreeView toolsTreeView;
        private System.Windows.Forms.Label sidebarLabel;
        private System.Windows.Forms.Panel scrollPanel;
        private System.Windows.Forms.TableLayoutPanel tableLayoutPanel1;
        private AntdUI.Button btnReleaseMemory;
        private AntdUI.Button btnChangePassword;
        private AntdUI.Button btnNvtOa;

        private AntdUI.Button btnSearchFiles;
        private AntdUI.Button btnPrivacyLock;

        private AntdUI.Button btnChatGPT;
        private AntdUI.Button btnFileConvert;

        private AntdUI.Button btnBaidu;
        private AntdUI.Button btnDeepSeek;
        private AntdUI.Button btnMindMap;
        private AntdUI.Button btnActivationTool;
        private AntdUI.Button btnIPInfo;
        private AntdUI.Button btnPPTAssistant;
        private AntdUI.Button btnXiaoHuanXiong;
        private AntdUI.Button btnDigitalHuman;
        private AntdUI.Button btnTextToImage;
        private AntdUI.Button btnTextToVideo;
        private AntdUI.Button btnTextToVoice;
        private AntdUI.Button btnTextToPPT;
        private AntdUI.Alert alertNotification;
        private AntdUI.Label label1;
        private Sunisoft.IrisSkin.SkinEngine skinEngine1;
    }
}