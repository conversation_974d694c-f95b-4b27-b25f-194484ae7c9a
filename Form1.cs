﻿using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using AntdUI;

namespace 工具箱
{
    public partial class Form1 : Form
    {


        public Form1()
        {
            InitializeComponent();

            // 设置 DPI 感知
            this.AutoScaleMode = AutoScaleMode.Dpi;
            // 将事件处理程序附加到按钮
            this.btnChatGPT.Click += new System.EventHandler(this.btnChatGPT_Click);
            this.btnNvtOa.Click += new System.EventHandler(this.btnNvtOa_Click);
            this.btnBaidu.Click += new System.EventHandler(this.btnBaidu_Click);
            this.skinEngine1.SkinFile = "RealOne.ssk"; //DiamondBlue.ssk


            this.btnReleaseMemory.Click += new System.EventHandler(this.btnReleaseMemory_Click);
            this.btnSearchFiles.Click += new System.EventHandler(this.btnSearchFiles_Click);

            this.btnChangePassword.Click += new System.EventHandler(this.btnChangePassword_Click);
            this.btnPrivacyLock.Click += new System.EventHandler(this.btnPrivacyLock_Click);
            this.btnFileConvert.Click += new System.EventHandler(this.btnFileConvert_Click);
            this.btnDeepSeek.Click += new System.EventHandler(this.btnDeepSeek_Click);
            this.btnMindMap.Click += new System.EventHandler(this.btnMindMap_Click);
            this.btnActivationTool.Click += new System.EventHandler(this.btnActivationTool_Click);
            this.btnIPInfo.Click += new System.EventHandler(this.btnIPInfo_Click);
            //this.btnPPTAssistant.Click += new System.EventHandler(this.btnPPTAssistant_Click);
            // 其他按钮的事件可以在这里添加

            // 只在运行时执行初始化，避免在设计时干扰设计器
            if (!this.DesignMode)
            {
                // 初始化 Select 组件
                InitializeSelectFunction();

                // 初始化侧边栏
                InitializeSidebar();

                // 加载通知信息
                LoadNotificationText();

                // 绑定帮助按钮事件
                this.HelpButtonClicked += Form1_HelpButtonClicked;

                // 绑定窗体加载事件
                this.Load += Form1_Load;
            }
        }



        private void btnChatGPT_Click(object sender, EventArgs e)
        {
            AntdUI.Message.info(this, "正在打开 ChatGPT...", null, 1);
            OpenUrl("https://fastgpt.atlbattery.com/chat/share?shareId=652f6aaec6e21c41b67348d5");
        }

        private void btnNvtOa_Click(object sender, EventArgs e)
        {
            AntdUI.Message.info(this, "正在打开 NVT-OA...", null, 1);
            OpenUrl("https://oa.nvtpower.com/login_single_random.jsp");
        }

        private void btnBaidu_Click(object sender, EventArgs e)
        {
            AntdUI.Message.info(this, "正在打开 百度...", null, 1);
            OpenUrl("https://www.baidu.com/");
        }







        private void btnReleaseMemory_Click(object sender, EventArgs e)
        {
            AntdUI.Message.info(this, "正在打开 释放系统内存...", null, 1);
            var result = MessageBox.Show("此操作将删除系统临时文件，是否继续？", "确认", MessageBoxButtons.YesNo, MessageBoxIcon.Warning);
            if (result == DialogResult.Yes)
            {
                using (var progressForm = new CleanupProgressForm())
                {
                    progressForm.ShowDialog();
                }
            }
        }

        private void btnSearchFiles_Click(object sender, EventArgs e)
        {
            AntdUI.Message.info(this, "正在打开 搜索文件...", null, 1);
            using (var searchForm = new FileSearchForm())
            {
                searchForm.ShowDialog();
            }
        }

        private void btnPrivacyLock_Click(object sender, EventArgs e)
        {
            AntdUI.Message.info(this, "正在打开 FineBI商业智能...", null, 1);
            OpenUrl("http://172.18.21.14:37799/webroot/decision/login?origin=1a7fbec8-c32a-41b7-bbb4-acbe8dbf7e21");
        }



        private void btnChangePassword_Click(object sender, EventArgs e)
        {
            AntdUI.Message.info(this, "正在打开 修改计算机密码...", null, 1);
            try
            {
                // 显示操作提示
                var result = MessageBox.Show(
                    "即将打开Windows密码修改界面。\n\n" +
                    "请注意：\n" +
                    "• 您需要知道当前密码才能修改\n" +
                    "• 新密码建议包含大小写字母、数字和特殊字符\n" +
                    "• 密码长度建议8位以上\n\n" +
                    "是否继续？",
                    "修改计算机密码",
                    MessageBoxButtons.YesNo,
                    MessageBoxIcon.Question);

                if (result != DialogResult.Yes)
                    return;

                // 检测Windows版本并使用最合适的方法
                var osVersion = Environment.OSVersion.Version;
                bool success = false;

                if (osVersion.Major >= 10) // Windows 10/11
                {
                    success = OpenWindowsPasswordSettings();
                }
                else if (osVersion.Major >= 6) // Windows Vista/7/8/8.1
                {
                    success = OpenControlPanelUserAccounts();
                }
                else
                {
                    success = OpenLegacyUserAccounts();
                }

                if (success)
                {
                    MessageBox.Show("密码修改界面已打开。\n\n请按照界面提示完成密码修改。",
                        "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                ShowPasswordChangeHelp(ex.Message);
            }
        }

        private void ShowPasswordChangeHelp(string errorMessage = "")
        {
            string helpMessage = "无法自动打开密码修改界面。\n\n";

            if (!string.IsNullOrEmpty(errorMessage))
            {
                helpMessage += $"错误信息: {errorMessage}\n\n";
            }

            helpMessage += "请手动按以下步骤修改密码：\n\n";

            var osVersion = Environment.OSVersion.Version;
            if (osVersion.Major >= 10) // Windows 10/11
            {
                helpMessage += "Windows 10/11:\n" +
                              "1. 按 Win + I 打开设置\n" +
                              "2. 点击\"账户\"\n" +
                              "3. 选择\"登录选项\"\n" +
                              "4. 点击\"密码\"下的\"更改\"按钮\n\n" +
                              "或者:\n" +
                              "按 Ctrl + Alt + Del，选择\"更改密码\"";
            }
            else if (osVersion.Major >= 6) // Windows Vista/7/8/8.1
            {
                helpMessage += "Windows Vista/7/8/8.1:\n" +
                              "1. 打开控制面板\n" +
                              "2. 点击\"用户账户\"\n" +
                              "3. 选择\"更改密码\"\n\n" +
                              "或者:\n" +
                              "按 Ctrl + Alt + Del，选择\"更改密码\"";
            }
            else
            {
                helpMessage += "Windows XP:\n" +
                              "1. 按 Ctrl + Alt + Del\n" +
                              "2. 选择\"更改密码\"\n\n" +
                              "或者:\n" +
                              "控制面板 → 用户账户 → 更改密码";
            }

            MessageBox.Show(helpMessage, "密码修改帮助", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private bool OpenWindowsPasswordSettings()
        {
            try
            {
                // Windows 10/11: 直接打开设置应用的登录选项页面
                Process.Start("ms-settings:signinoptions");
                return true;
            }
            catch
            {
                try
                {
                    // 备选方案1: 打开账户设置页面
                    Process.Start("ms-settings:yourinfo");
                    return true;
                }
                catch
                {
                    try
                    {
                        // 备选方案2: 使用传统的用户账户控制面板
                        Process.Start("netplwiz");
                        return true;
                    }
                    catch
                    {
                        // 备选方案3: 打开控制面板
                        return OpenControlPanelUserAccounts();
                    }
                }
            }
        }

        private bool OpenControlPanelUserAccounts()
        {
            try
            {
                // 打开控制面板的用户账户
                Process.Start("control", "userpasswords");
                return true;
            }
            catch
            {
                try
                {
                    // 备选方案1: 使用netplwiz
                    Process.Start("netplwiz");
                    return true;
                }
                catch
                {
                    try
                    {
                        // 备选方案2: 打开控制面板主页
                        Process.Start("control");
                        return true;
                    }
                    catch
                    {
                        return false;
                    }
                }
            }
        }

        private bool OpenLegacyUserAccounts()
        {
            try
            {
                // Windows XP/Vista: 使用传统的用户账户面板
                Process.Start("control", "userpasswords2");
                return true;
            }
            catch
            {
                // 备选方案
                return OpenControlPanelUserAccounts();
            }
        }

        private void btnFileConvert_Click(object sender, EventArgs e)
        {
            AntdUI.Message.info(this, "正在打开 文件转换...", null, 1);
            OpenUrl("https://tools.pdf24.org/zh/edit-pdf");
        }

        private void btnDeepSeek_Click(object sender, EventArgs e)
        {
            AntdUI.Message.info(this, "正在打开 DeepSeek...", null, 1);
            OpenUrl("https://chat.deepseek.com/sign_in");
        }

        private void btnMindMap_Click(object sender, EventArgs e)
        {
            AntdUI.Message.info(this, "正在打开 思维导图...", null, 1);
            OpenUrl("https://mermaid.shizhuoran.top/");
        }

        private void btnActivationTool_Click(object sender, EventArgs e)
        {
            AntdUI.Message.info(this, "正在启动激活工具...", null, 1);

            // 显示确认对话框
            var result = MessageBox.Show(
                "您即将运行系统激活工具，此操作可能会修改系统设置。\n\n是否确认继续？",
                "确认运行激活工具",
                MessageBoxButtons.YesNo,
                MessageBoxIcon.Warning);

            if (result == DialogResult.Yes)
            {
                try
                {
                    string activationToolPath = @"\\t-018254\临时文件中转\激活工具\KMS_VL_ALL_AIO_CN.cmd";

                    // 检查文件是否存在
                    if (System.IO.File.Exists(activationToolPath))
                    {
                        // 使用 ProcessStartInfo 来配置进程启动参数
                        ProcessStartInfo startInfo = new ProcessStartInfo
                        {
                            FileName = activationToolPath,
                            UseShellExecute = true,
                            Verb = "runas" // 以管理员权限运行
                        };

                        Process.Start(startInfo);
                        AntdUI.Message.success(this, "激活工具已启动", null, 1);
                    }
                    else
                    {
                        AntdUI.Message.error(this, "激活工具文件不存在，请检查网络连接或文件路径", null, 1);
                        MessageBox.Show(
                            $"无法找到激活工具文件：\n{activationToolPath}\n\n请确认：\n1. 网络连接正常\n2. 有权限访问该网络路径\n3. 文件确实存在",
                            "文件不存在",
                            MessageBoxButtons.OK,
                            MessageBoxIcon.Error);
                    }
                }
                catch (UnauthorizedAccessException)
                {
                    AntdUI.Message.error(this, "权限不足，请以管理员身份运行程序", null, 1);
                    MessageBox.Show(
                        "访问被拒绝。请确认：\n1. 以管理员身份运行本程序\n2. 有权限访问网络路径\n3. 网络驱动器已正确映射",
                        "权限错误",
                        MessageBoxButtons.OK,
                        MessageBoxIcon.Error);
                }
                catch (System.ComponentModel.Win32Exception ex)
                {
                    AntdUI.Message.error(this, "启动激活工具失败", null, 1);
                    MessageBox.Show(
                        $"无法启动激活工具：\n{ex.Message}\n\n可能的原因：\n1. 用户取消了UAC提示\n2. 系统权限不足\n3. 文件损坏或不可执行",
                        "启动失败",
                        MessageBoxButtons.OK,
                        MessageBoxIcon.Error);
                }
                catch (Exception ex)
                {
                    AntdUI.Message.error(this, "发生未知错误", null, 1);
                    MessageBox.Show(
                        $"启动激活工具时发生错误：\n{ex.Message}",
                        "错误",
                        MessageBoxButtons.OK,
                        MessageBoxIcon.Error);
                }
            }
        }

        private void btnIPInfo_Click(object sender, EventArgs e)
        {
            AntdUI.Message.info(this, "正在获取IP信息...", null, 1);

            try
            {
                // 获取计算机名称
                string computerName = Environment.MachineName;

                // 获取用户名
                string userName = Environment.UserName;

                // 获取IP地址
                string ipAddress = GetLocalIPAddress();

                // 获取域名（如果有）
                string domainName = Environment.UserDomainName;

                // 获取操作系统信息
                string osVersion = Environment.OSVersion.ToString();

                // 构建信息字符串
                StringBuilder infoBuilder = new StringBuilder();
                infoBuilder.AppendLine("=== 计算机网络信息 ===");
                infoBuilder.AppendLine();
                infoBuilder.AppendLine($"计算机名称：{computerName}");
                infoBuilder.AppendLine($"用户名：{userName}");
                infoBuilder.AppendLine($"域名：{domainName}");
                infoBuilder.AppendLine($"IP地址：{ipAddress}");
                infoBuilder.AppendLine($"操作系统：{osVersion}");
                infoBuilder.AppendLine();
                infoBuilder.AppendLine("=== 网络适配器信息 ===");

                // 获取详细的网络适配器信息
                var networkInfo = GetNetworkAdapterInfo();
                infoBuilder.AppendLine(networkInfo);

                // 显示信息
                MessageBox.Show(
                    infoBuilder.ToString(),
                    "IP信息查看",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Information);

                AntdUI.Message.success(this, "IP信息获取完成", null, 1);
            }
            catch (Exception ex)
            {
                AntdUI.Message.error(this, "获取IP信息失败", null, 1);
                MessageBox.Show(
                    $"获取IP信息时发生错误：\n{ex.Message}",
                    "错误",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 获取本地IP地址
        /// </summary>
        /// <returns>本地IP地址</returns>
        private string GetLocalIPAddress()
        {
            try
            {
                var host = System.Net.Dns.GetHostEntry(System.Net.Dns.GetHostName());
                foreach (var ip in host.AddressList)
                {
                    if (ip.AddressFamily == System.Net.Sockets.AddressFamily.InterNetwork)
                    {
                        return ip.ToString();
                    }
                }
                return "未找到IPv4地址";
            }
            catch
            {
                return "获取IP地址失败";
            }
        }

        /// <summary>
        /// 获取网络适配器详细信息
        /// </summary>
        /// <returns>网络适配器信息字符串</returns>
        private string GetNetworkAdapterInfo()
        {
            try
            {
                StringBuilder info = new StringBuilder();
                var networkInterfaces = System.Net.NetworkInformation.NetworkInterface.GetAllNetworkInterfaces();

                foreach (var adapter in networkInterfaces)
                {
                    if (adapter.OperationalStatus == System.Net.NetworkInformation.OperationalStatus.Up &&
                        adapter.NetworkInterfaceType != System.Net.NetworkInformation.NetworkInterfaceType.Loopback)
                    {
                        info.AppendLine($"适配器名称：{adapter.Name}");
                        info.AppendLine($"描述：{adapter.Description}");
                        info.AppendLine($"类型：{adapter.NetworkInterfaceType}");
                        info.AppendLine($"状态：{adapter.OperationalStatus}");
                        info.AppendLine($"速度：{adapter.Speed / 1000000} Mbps");

                        var properties = adapter.GetIPProperties();
                        foreach (var unicast in properties.UnicastAddresses)
                        {
                            if (unicast.Address.AddressFamily == System.Net.Sockets.AddressFamily.InterNetwork)
                            {
                                info.AppendLine($"IP地址：{unicast.Address}");
                                info.AppendLine($"子网掩码：{unicast.IPv4Mask}");
                            }
                        }

                        if (properties.GatewayAddresses.Count > 0)
                        {
                            info.AppendLine($"默认网关：{properties.GatewayAddresses[0].Address}");
                        }

                        info.AppendLine("---");
                    }
                }

                return info.ToString();
            }
            catch (Exception ex)
            {
                return $"获取网络适配器信息失败：{ex.Message}";
            }
        }

        private void btnPPTAssistant_Click(object sender, EventArgs e)
        {
            AntdUI.Message.info(this, "正在打开 PPT助手...", null, 1);
            OpenUrl("https://kimi.moonshot.cn/kimiplus/cvvm7bkheutnihqi2100");
        }

        private void btnXiaoHuanXiong_Click(object sender, EventArgs e)
        {
            AntdUI.Message.info(this, "正在打开 小浣熊...", null, 1);
            OpenUrl("https://xiaohuanxiong.com/");
        }

        private void btnDigitalHuman_Click(object sender, EventArgs e)
        {
            AntdUI.Message.info(this, "正在打开 数字人生成 (有言3D)...", null, 1);
            // 直接打开第一个网址，在消息中提示有多个选择
            OpenUrl("https://www.youyan3d.com/platform?from=drlx_yhaigc");
        }

        private void btnTextToImage_Click(object sender, EventArgs e)
        {
            AntdUI.Message.info(this, "正在打开 文生图 (LiblibAI)...", null, 1);
            // 直接打开第一个网址，在消息中提示有多个选择
            OpenUrl("https://www.liblib.art/?sourceId=000147&qhclickid=e2dba2386860b107");
        }

        private void btnTextToVideo_Click(object sender, EventArgs e)
        {
            AntdUI.Message.info(this, "正在打开 文生视频 (可灵AI)...", null, 1);
            // 直接打开第一个网址，在消息中提示有多个选择
            OpenUrl("https://app.klingai.com/cn/activity-zone?id=726928327164321859");
        }

        private void btnTextToVoice_Click(object sender, EventArgs e)
        {
            AntdUI.Message.info(this, "正在打开 文生声音 (可灵AI)...", null, 1);
            // 直接打开第一个网址，在消息中提示有多个选择
            OpenUrl("https://app.klingai.com/cn/activity-zone?id=726928327164321859");
        }

        private void btnTextToPPT_Click(object sender, EventArgs e)
        {
            AntdUI.Message.info(this, "正在打开 文生PPT...", null, 1);
            OpenUrl("https://kimi.moonshot.cn/kimiplus/conpg18t7lagbbsfqksg");
        }

        /// <summary>
        /// 帮助按钮点击事件处理程序
        /// </summary>
        /// <param name="sender">事件发送者</param>
        /// <param name="e">帮助事件参数</param>
        private void Form1_HelpButtonClicked(object sender, System.ComponentModel.CancelEventArgs e)
        {
            // 取消默认的帮助行为
            e.Cancel = true;

            // 显示自定义帮助信息
            ShowHelpDialog();
        }

        /// <summary>
        /// 显示帮助对话框
        /// </summary>
        private void ShowHelpDialog()
        {
            try
            {
                // 使用 AntdUI.Message 显示简短提示
                AntdUI.Message.info(this, "显示帮助信息...", null, 1);

                // 构建帮助信息内容
                string helpContent =
                    "=== 关于工具箱 ===\n\n" +
                    "此集成工具箱为NVT桌面运维独立开发\n\n" +
                    "如有问题或使用中出现问题请联系相关人员\n\n" +
                    "=== 使用说明 ===\n\n" +
                    "• 使用顶部搜索框可以快速查找功能\n" +
                    "• 点击对应按钮即可使用相关工具\n" +
                    "• 如果按钮较多，可以滚动查看更多功能\n\n" +
                    "版本信息：v1.0\n" +
                    "开发团队：NVT桌面运维";

                // 显示帮助对话框
                MessageBox.Show(
                    helpContent,
                    "关于工具箱",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Information);

                // 显示成功提示
                AntdUI.Message.success(this, "帮助信息已显示", null, 1);
            }
            catch (Exception ex)
            {
                // 错误处理
                AntdUI.Message.error(this, "显示帮助信息失败", null, 1);
                MessageBox.Show(
                    $"显示帮助信息时发生错误：\n{ex.Message}",
                    "错误",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Error);
            }
        }







        private void OpenUrl(string url)
        {
            try
            {
                Process.Start(url);
            }
            catch (Exception ex)
            {
                MessageBox.Show("无法打开链接: " + ex.Message, "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void InitializeSelectFunction()
        {
            // 清空 Items，不需要下拉选项
            selectFunction.Items.Clear();

            // 禁用下拉功能
            selectFunction.DropDownArrow = false;
            selectFunction.ClickSwitchDropdown = false;
            selectFunction.MaxCount = 0;

            // 绑定事件
            selectFunction.TextChanged += SelectFunction_TextChanged;
        }

        /// <summary>
        /// 初始化侧边栏
        /// </summary>
        private void InitializeSidebar()
        {
            try
            {
                // 确保侧边栏面板可见
                sidebarPanel.Visible = true;
                sidebarPanel.BringToFront();

                // 使用设计器中设置的大小，不强制覆盖
                // 只确保 TreeView 的位置正确（在 sidebarLabel 下方）
                int labelHeight = sidebarLabel.Height;
                if (toolsTreeView.Location.Y != labelHeight)
                {
                    toolsTreeView.Location = new Point(0, labelHeight);
                }

                // 清空树形控件（包括设计时添加的节点）
                toolsTreeView.Nodes.Clear();

                // 创建AI工具分类
                TreeNode aiToolsNode = new TreeNode("🤖 AI工具") { Tag = "AITools" };
                aiToolsNode.Nodes.Add(new TreeNode("🧠 DeepSeek") { Tag = "DeepSeek" });
                aiToolsNode.Nodes.Add(new TreeNode("💬 ChatGPT") { Tag = "ChatGPT" });
                aiToolsNode.Nodes.Add(new TreeNode("📊 PPT助手") { Tag = "PPTAssistant" });
                aiToolsNode.Nodes.Add(new TreeNode("🦝 小浣熊") { Tag = "XiaoHuanXiong" });
                aiToolsNode.Nodes.Add(new TreeNode("👤 数字人生成") { Tag = "DigitalHuman" });
                aiToolsNode.Nodes.Add(new TreeNode("🎨 文生图") { Tag = "TextToImage" });
                aiToolsNode.Nodes.Add(new TreeNode("🎬 文生视频") { Tag = "TextToVideo" });
                aiToolsNode.Nodes.Add(new TreeNode("🎵 文生声音") { Tag = "TextToVoice" });
                aiToolsNode.Nodes.Add(new TreeNode("📄 文生PPT") { Tag = "TextToPPT" });

                // 创建系统工具分类
                TreeNode systemToolsNode = new TreeNode("⚙️ 系统工具") { Tag = "SystemTools" };
                systemToolsNode.Nodes.Add(new TreeNode("🧹 释放系统内存") { Tag = "ReleaseMemory" });
                systemToolsNode.Nodes.Add(new TreeNode("🔐 修改计算机密码") { Tag = "ChangePassword" });
                systemToolsNode.Nodes.Add(new TreeNode("🌐 IP信息查看") { Tag = "IPInfo" });
                systemToolsNode.Nodes.Add(new TreeNode("🔑 激活工具") { Tag = "ActivationTool" });

                // 创建网络工具分类
                TreeNode networkToolsNode = new TreeNode("🌍 网络工具") { Tag = "NetworkTools" };
                networkToolsNode.Nodes.Add(new TreeNode("🏢 NVT-OA") { Tag = "NvtOa" });
                networkToolsNode.Nodes.Add(new TreeNode("🔍 百度") { Tag = "Baidu" });
                networkToolsNode.Nodes.Add(new TreeNode("🔄 文件转换") { Tag = "FileConvert" });

                // 创建实用工具分类
                TreeNode utilityToolsNode = new TreeNode("🛠️ 实用工具") { Tag = "UtilityTools" };
                utilityToolsNode.Nodes.Add(new TreeNode("📁 搜索文件") { Tag = "SearchFiles" });
                utilityToolsNode.Nodes.Add(new TreeNode("🗺️ 思维导图") { Tag = "MindMap" });
                utilityToolsNode.Nodes.Add(new TreeNode("📈 FineBI商业智能") { Tag = "PrivacyLock" });

                // 创建"显示全部"节点
                TreeNode showAllNode = new TreeNode("📋 显示全部") { Tag = "ShowAll" };

                // 添加所有分类到树形控件
                toolsTreeView.Nodes.Add(showAllNode);
                toolsTreeView.Nodes.Add(aiToolsNode);
                toolsTreeView.Nodes.Add(systemToolsNode);
                toolsTreeView.Nodes.Add(networkToolsNode);
                toolsTreeView.Nodes.Add(utilityToolsNode);

                // 默认选中"显示全部"节点
                toolsTreeView.SelectedNode = showAllNode;

                // 设置现代化的树形控件样式，调整ItemHeight以确保文本完整显示
                toolsTreeView.ItemHeight = 45;
                toolsTreeView.Indent = 20;
                toolsTreeView.ShowLines = false;
                toolsTreeView.ShowPlusMinus = true;
                toolsTreeView.ShowRootLines = false;
                toolsTreeView.HotTracking = true;
                toolsTreeView.BackColor = Color.FromArgb(248, 249, 250);

                // 设置支持 Emoji 的字体
                try
                {
                    toolsTreeView.Font = new Font("Segoe UI Emoji", 10f, FontStyle.Regular);
                }
                catch
                {
                    try
                    {
                        toolsTreeView.Font = new Font("Microsoft YaHei UI", 10f, FontStyle.Regular);
                    }
                    catch
                    {
                        try
                        {
                            toolsTreeView.Font = new Font("微软雅黑", 10f, FontStyle.Regular);
                        }
                        catch
                        {
                            // 如果都不可用，保持默认字体
                            System.Diagnostics.Debug.WriteLine("无法设置支持 Emoji 的字体，使用默认字体");
                        }
                    }
                }

                // 绑定树形控件事件
                toolsTreeView.NodeMouseClick += ToolsTreeView_NodeMouseClick;

                // 启用自定义绘制以控制悬停效果样式
                if (!this.DesignMode)
                {
                    toolsTreeView.DrawMode = TreeViewDrawMode.OwnerDrawText;
                    toolsTreeView.DrawNode += ToolsTreeView_DrawNode;
                }

                // 确保控件可见
                toolsTreeView.Visible = true;
                sidebarLabel.Visible = true;

                // 默认显示所有按钮
                ShowAllButtons();

                // 检查 Emoji 显示是否正常，如果不正常则使用文本替代
                CheckAndFixEmojiDisplay();

                // 验证按钮是否存在
                ValidateButtons();

                System.Diagnostics.Debug.WriteLine("侧边栏初始化完成");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"侧边栏初始化失败: {ex.Message}");
                MessageBox.Show($"侧边栏初始化失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 验证所有按钮是否存在
        /// </summary>
        private void ValidateButtons()
        {
            var buttonNames = new[]
            {
                "btnDeepSeek", "btnChatGPT", "btnPPTAssistant", "btnXiaoHuanXiong", "btnDigitalHuman", "btnTextToImage", "btnTextToVideo", "btnTextToVoice", "btnTextToPPT",
                "btnReleaseMemory", "btnChangePassword", "btnIPInfo", "btnActivationTool",
                "btnNvtOa", "btnBaidu", "btnFileConvert",
                "btnSearchFiles", "btnMindMap", "btnPrivacyLock"
            };

            var buttons = new[]
            {
                btnDeepSeek, btnChatGPT, btnPPTAssistant, btnXiaoHuanXiong, btnDigitalHuman, btnTextToImage, btnTextToVideo, btnTextToVoice, btnTextToPPT,
                btnReleaseMemory, btnChangePassword, btnIPInfo, btnActivationTool,
                btnNvtOa, btnBaidu, btnFileConvert,
                btnSearchFiles, btnMindMap, btnPrivacyLock
            };

            for (int i = 0; i < buttons.Length; i++)
            {
                if (buttons[i] == null)
                {
                    System.Diagnostics.Debug.WriteLine($"警告: 按钮 {buttonNames[i]} 为 null");
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine($"按钮 {buttonNames[i]} 存在");
                }
            }
        }

        /// <summary>
        /// 窗体加载事件处理程序
        /// </summary>
        /// <param name="sender">事件发送者</param>
        /// <param name="e">事件参数</param>
        private void Form1_Load(object sender, EventArgs e)
        {
            try
            {
                // 强制刷新布局
                this.PerformLayout();
                sidebarPanel.PerformLayout();

                // 确保侧边栏显示正确
                sidebarPanel.Invalidate();
                toolsTreeView.Invalidate();

                // 确保标题没有下划线
                label1.Font = new Font("幼圆", 15F, FontStyle.Bold, GraphicsUnit.Point);

                System.Diagnostics.Debug.WriteLine("窗体加载完成，布局已刷新");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"窗体加载时发生错误: {ex.Message}");
            }
        }







        /// <summary>
        /// 显示所有控件
        /// </summary>
        private void ShowAllControls()
        {
            // 暂停布局更新以提高性能
            tableLayoutPanel1.SuspendLayout();

            try
            {
                // 遍历 tableLayoutPanel1 中的所有控件，设置为可见
                foreach (Control control in tableLayoutPanel1.Controls)
                {
                    control.Visible = true;
                }
            }
            finally
            {
                // 恢复布局更新
                tableLayoutPanel1.ResumeLayout(true);
            }
        }

        /// <summary>
        /// Select 组件文本变化事件处理程序 - 实现实时搜索过滤
        /// </summary>
        /// <param name="sender">事件发送者</param>
        /// <param name="e">事件参数</param>
        private void SelectFunction_TextChanged(object sender, EventArgs e)
        {
            string searchText = selectFunction.Text?.Trim() ?? "";

            // 如果搜索文本为空，显示所有按钮
            if (string.IsNullOrWhiteSpace(searchText))
            {
                ShowAllButtons();
                return;
            }

            // 根据搜索文本实时过滤控件显示
            FilterControlsBySearchText(searchText);
        }

        /// <summary>
        /// 根据搜索文本过滤控件显示
        /// </summary>
        /// <param name="searchText">搜索文本</param>
        private void FilterControlsBySearchText(string searchText)
        {
            // 暂停布局更新以提高性能
            tableLayoutPanel1.SuspendLayout();

            try
            {
                // 遍历 tableLayoutPanel1 中的所有控件
                foreach (Control control in tableLayoutPanel1.Controls)
                {
                    bool shouldShow = false;

                    // 检查是否为 AntdUI.Button 控件
                    if (control is AntdUI.Button button)
                    {
                        // 获取按钮的文本
                        string buttonText = button.Text ?? "";

                        // 进行模糊匹配（不区分大小写）
                        shouldShow = buttonText.IndexOf(searchText, StringComparison.OrdinalIgnoreCase) >= 0;
                    }
                    else
                    {
                        // 对于非按钮控件，也可以进行文本匹配（如果有Text属性）
                        var textProperty = control.GetType().GetProperty("Text");
                        if (textProperty != null)
                        {
                            string controlText = textProperty.GetValue(control)?.ToString() ?? "";
                            shouldShow = controlText.IndexOf(searchText, StringComparison.OrdinalIgnoreCase) >= 0;
                        }
                    }

                    // 设置控件的可见性
                    control.Visible = shouldShow;
                }
            }
            finally
            {
                // 恢复布局更新
                tableLayoutPanel1.ResumeLayout(true);
            }
        }





        private void LoadNotificationText()
        {
            try
            {
                string filePath = @"\\t-018254\临时文件中转\5555\11.txt";

                if (System.IO.File.Exists(filePath))
                {
                    string content = System.IO.File.ReadAllText(filePath, System.Text.Encoding.UTF8);
                    if (!string.IsNullOrWhiteSpace(content))
                    {
                        alertNotification.Text = content.Trim();
                    }
                    else
                    {
                        alertNotification.Text = "通知文件为空";
                    }
                }
                else
                {
                    alertNotification.Text = "无法访问通知文件，请检查网络连接";
                }
            }
            catch (UnauthorizedAccessException)
            {
                alertNotification.Text = "没有权限访问通知文件";
            }
            catch (System.IO.DirectoryNotFoundException)
            {
                alertNotification.Text = "通知文件路径不存在";
            }
            catch (System.IO.FileNotFoundException)
            {
                alertNotification.Text = "通知文件未找到";
            }
            catch (Exception ex)
            {
                alertNotification.Text = $"加载通知失败: {ex.Message}";
            }
        }

        /// <summary>
        /// 动态添加按钮到 TableLayoutPanel
        /// </summary>
        /// <param name="buttonText">按钮文本</param>
        /// <param name="clickHandler">点击事件处理方法</param>
        public void AddButton(string buttonText, EventHandler clickHandler)
        {
            // 计算当前按钮数量
            int currentButtonCount = tableLayoutPanel1.Controls.Count;
            int columns = tableLayoutPanel1.ColumnCount;

            // 计算新按钮的位置
            int row = currentButtonCount / columns;
            int col = currentButtonCount % columns;

            // 如果需要新行，增加行数
            if (row >= tableLayoutPanel1.RowCount)
            {
                tableLayoutPanel1.RowCount = row + 1;
                tableLayoutPanel1.RowStyles.Add(new RowStyle(SizeType.Absolute, 100F));
            }

            // 创建新按钮
            var newButton = new AntdUI.Button
            {
                Text = buttonText,
                Dock = DockStyle.Fill,
                TabIndex = currentButtonCount + 20 // 避免与现有按钮冲突
            };

            // 绑定点击事件
            if (clickHandler != null)
            {
                newButton.Click += clickHandler;
            }

            // 添加到 TableLayoutPanel
            tableLayoutPanel1.Controls.Add(newButton, col, row);
        }

        /// <summary>
        /// 获取当前 TableLayoutPanel 中的按钮数量
        /// </summary>
        /// <returns>按钮数量</returns>
        public int GetButtonCount()
        {
            return tableLayoutPanel1.Controls.Count;
        }

        /// <summary>
        /// 清除所有动态添加的按钮（保留原有按钮）
        /// </summary>
        public void ClearDynamicButtons()
        {
            // 这里可以根据需要实现清除逻辑
            // 例如，只保留前11个按钮（当前的固定按钮）
            var controlsToRemove = new List<Control>();

            for (int i = 11; i < tableLayoutPanel1.Controls.Count; i++)
            {
                controlsToRemove.Add(tableLayoutPanel1.Controls[i]);
            }

            foreach (var control in controlsToRemove)
            {
                tableLayoutPanel1.Controls.Remove(control);
                control.Dispose();
            }
        }

        /// <summary>
        /// AI工具列表框选择事件处理程序
        /// </summary>
        /// <param name="sender">事件发送者</param>
        /// <param name="e">事件参数</param>
        /// <summary>
        /// 树形控件节点点击事件处理程序
        /// </summary>
        /// <param name="sender">事件发送者</param>
        /// <param name="e">事件参数</param>
        private void ToolsTreeView_NodeMouseClick(object sender, TreeNodeMouseClickEventArgs e)
        {
            // 处理"显示全部"节点的点击
            if (e.Node.Tag != null && e.Node.Tag.ToString() == "ShowAll")
            {
                HandleShowAllClick();
                return;
            }

            // 处理分类节点的点击（有子节点的节点）
            if (e.Node.Nodes.Count > 0)
            {
                // 优先使用 Tag 属性，如果没有则使用文本
                string categoryIdentifier = e.Node.Tag?.ToString() ?? e.Node.Text;
                HandleCategoryClick(categoryIdentifier);
                return;
            }

            // 处理工具项目节点的点击（叶子节点）
            if (e.Node.Tag == null) return;

            string toolTag = e.Node.Tag.ToString();
            string toolName = e.Node.Text;

            try
            {
                // 显示选择提示
                AntdUI.Message.info(this, $"正在启动 {toolName}...", null, 1);

                // 根据选择的工具触发对应按钮的点击事件
                switch (toolTag)
                {
                    // AI工具
                    case "DeepSeek":
                        HighlightButton(btnDeepSeek);
                        btnDeepSeek_Click(this, EventArgs.Empty);
                        break;
                    case "ChatGPT":
                        HighlightButton(btnChatGPT);
                        btnChatGPT_Click(this, EventArgs.Empty);
                        break;
                    case "PPTAssistant":
                        HighlightButton(btnPPTAssistant);
                        btnPPTAssistant_Click(this, EventArgs.Empty);
                        break;
                    case "XiaoHuanXiong":
                        HighlightButton(btnXiaoHuanXiong);
                        btnXiaoHuanXiong_Click(this, EventArgs.Empty);
                        break;
                    case "DigitalHuman":
                        HighlightButton(btnDigitalHuman);
                        btnDigitalHuman_Click(this, EventArgs.Empty);
                        break;
                    case "TextToImage":
                        HighlightButton(btnTextToImage);
                        btnTextToImage_Click(this, EventArgs.Empty);
                        break;
                    case "TextToVideo":
                        HighlightButton(btnTextToVideo);
                        btnTextToVideo_Click(this, EventArgs.Empty);
                        break;
                    case "TextToVoice":
                        HighlightButton(btnTextToVoice);
                        btnTextToVoice_Click(this, EventArgs.Empty);
                        break;
                    case "TextToPPT":
                        HighlightButton(btnTextToPPT);
                        btnTextToPPT_Click(this, EventArgs.Empty);
                        break;

                    // 系统工具
                    case "ReleaseMemory":
                        HighlightButton(btnReleaseMemory);
                        btnReleaseMemory_Click(this, EventArgs.Empty);
                        break;
                    case "ChangePassword":
                        HighlightButton(btnChangePassword);
                        btnChangePassword_Click(this, EventArgs.Empty);
                        break;
                    case "IPInfo":
                        HighlightButton(btnIPInfo);
                        btnIPInfo_Click(this, EventArgs.Empty);
                        break;
                    case "ActivationTool":
                        HighlightButton(btnActivationTool);
                        btnActivationTool_Click(this, EventArgs.Empty);
                        break;

                    // 网络工具
                    case "NvtOa":
                        HighlightButton(btnNvtOa);
                        btnNvtOa_Click(this, EventArgs.Empty);
                        break;
                    case "Baidu":
                        HighlightButton(btnBaidu);
                        btnBaidu_Click(this, EventArgs.Empty);
                        break;
                    case "FileConvert":
                        HighlightButton(btnFileConvert);
                        btnFileConvert_Click(this, EventArgs.Empty);
                        break;

                    // 实用工具
                    case "SearchFiles":
                        HighlightButton(btnSearchFiles);
                        btnSearchFiles_Click(this, EventArgs.Empty);
                        break;
                    case "MindMap":
                        HighlightButton(btnMindMap);
                        btnMindMap_Click(this, EventArgs.Empty);
                        break;
                    case "PrivacyLock":
                        HighlightButton(btnPrivacyLock);
                        btnPrivacyLock_Click(this, EventArgs.Empty);
                        break;

                    default:
                        AntdUI.Message.warn(this, $"未找到对应的工具: {toolName}", null, 1);
                        break;
                }
            }
            catch (Exception ex)
            {
                AntdUI.Message.error(this, "启动工具失败", null, 1);
                MessageBox.Show(
                    $"启动 {toolName} 时发生错误：\n{ex.Message}",
                    "错误",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 高亮显示指定按钮
        /// </summary>
        /// <param name="button">要高亮的按钮</param>
        private void HighlightButton(AntdUI.Button button)
        {
            if (button == null) return;

            try
            {
                // 临时改变按钮背景色以实现高亮效果
                var originalBackColor = button.BackColor;
                button.BackColor = System.Drawing.Color.LightBlue;

                // 使用定时器恢复原始颜色
                var timer = new System.Windows.Forms.Timer();
                timer.Interval = 1000; // 1秒后恢复
                timer.Tick += (s, e) =>
                {
                    button.BackColor = originalBackColor;
                    timer.Stop();
                    timer.Dispose();
                };
                timer.Start();

                // 确保按钮可见（滚动到按钮位置）
                button.Focus();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"高亮按钮失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 自定义绘制 TreeView 节点
        /// </summary>
        /// <param name="sender">事件发送者</param>
        /// <param name="e">绘制事件参数</param>
        private void ToolsTreeView_DrawNode(object sender, DrawTreeNodeEventArgs e)
        {
            try
            {
                // 确保绘制区域有效
                if (e.Bounds.Width <= 0 || e.Bounds.Height <= 0)
                {
                    e.DrawDefault = true;
                    return;
                }

                // 设置背景色
                Color backColor;
                Color textColor;

                if (e.Node.IsSelected)
                {
                    // 选中状态
                    backColor = Color.FromArgb(52, 144, 220);
                    textColor = Color.White;
                }
                else if ((e.State & TreeNodeStates.Hot) == TreeNodeStates.Hot)
                {
                    // 悬停状态
                    backColor = Color.FromArgb(230, 240, 250);
                    textColor = Color.FromArgb(52, 73, 94);
                }
                else
                {
                    // 默认状态
                    backColor = Color.FromArgb(248, 249, 250);
                    textColor = Color.FromArgb(52, 73, 94);
                }

                // 绘制背景
                using (SolidBrush backBrush = new SolidBrush(backColor))
                {
                    e.Graphics.FillRectangle(backBrush, e.Bounds);
                }

                // 绘制文本
                using (SolidBrush textBrush = new SolidBrush(textColor))
                {
                    // 使用支持 Emoji 的字体
                    Font nodeFont;
                    try
                    {
                        // 优先使用 Segoe UI Emoji 字体，使用与设计器一致的字体大小
                        nodeFont = new Font("Segoe UI Emoji", 10f, FontStyle.Regular);
                    }
                    catch
                    {
                        // 如果 Segoe UI Emoji 不可用，使用 Microsoft YaHei UI
                        try
                        {
                            nodeFont = new Font("Microsoft YaHei UI", 10f, FontStyle.Regular);
                        }
                        catch
                        {
                            // 最后回退到默认字体
                            nodeFont = e.Node.NodeFont ?? toolsTreeView.Font;
                        }
                    }

                    // 为分类节点使用稍大的字体
                    if (e.Node.Nodes.Count > 0 || e.Node.Tag?.ToString() == "ShowAll")
                    {
                        try
                        {
                            nodeFont = new Font(nodeFont.FontFamily, nodeFont.Size + 0.5f, FontStyle.Bold);
                        }
                        catch
                        {
                            // 如果创建粗体字体失败，使用原字体
                        }
                    }

                    // 优化文本绘制区域，确保文本完整显示
                    Rectangle textBounds = new Rectangle(
                        e.Bounds.X + 3,
                        e.Bounds.Y,
                        Math.Max(1, e.Bounds.Width - 6),
                        Math.Max(1, e.Bounds.Height));

                    // 优化字符串格式设置，确保文本正确对齐和显示
                    StringFormat stringFormat = new StringFormat
                    {
                        LineAlignment = StringAlignment.Center,
                        Alignment = StringAlignment.Near,
                        Trimming = StringTrimming.EllipsisCharacter,
                        FormatFlags = StringFormatFlags.NoWrap | StringFormatFlags.FitBlackBox
                    };

                    e.Graphics.DrawString(e.Node.Text, nodeFont, textBrush, textBounds, stringFormat);
                }

                // 绘制焦点框（如果需要）
                if ((e.State & TreeNodeStates.Focused) == TreeNodeStates.Focused)
                {
                    using (Pen focusPen = new Pen(Color.FromArgb(52, 144, 220), 1))
                    {
                        focusPen.DashStyle = System.Drawing.Drawing2D.DashStyle.Dot;
                        Rectangle focusRect = new Rectangle(e.Bounds.X + 1, e.Bounds.Y + 1,
                            e.Bounds.Width - 2, e.Bounds.Height - 2);
                        e.Graphics.DrawRectangle(focusPen, focusRect);
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"绘制TreeView节点失败: {ex.Message}");
                // 如果自定义绘制失败，使用默认绘制
                e.DrawDefault = true;
            }
        }

        /// <summary>
        /// 处理"显示全部"节点点击事件
        /// </summary>
        private void HandleShowAllClick()
        {
            try
            {
                // 显示所有按钮
                ShowAllButtons();
            }
            catch (Exception ex)
            {
                MessageBox.Show(
                    $"显示所有工具时发生错误：\n{ex.Message}",
                    "错误",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 处理分类节点点击事件
        /// </summary>
        /// <param name="categoryName">分类名称</param>
        private void HandleCategoryClick(string categoryName)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine($"点击分类: {categoryName}");

                // 根据分类名称过滤显示按钮
                FilterButtonsByCategory(categoryName);

                // 显示成功提示
                AntdUI.Message.info(this, $"已切换到 {categoryName} 分类", null, 1);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"处理分类点击失败: {ex.Message}");
                MessageBox.Show(
                    $"过滤 {categoryName} 时发生错误：\n{ex.Message}",
                    "错误",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 根据分类过滤显示按钮
        /// </summary>
        /// <param name="categoryName">分类名称</param>
        private void FilterButtonsByCategory(string categoryName)
        {
            // 暂停布局更新以提高性能
            tableLayoutPanel1.SuspendLayout();

            try
            {
                // 定义分类与按钮的映射关系（支持 Tag 和文本两种方式）
                var categoryButtonMapping = new Dictionary<string, List<AntdUI.Button>>
                {
                    // 使用 Tag 方式
                    {
                        "AITools",
                        new List<AntdUI.Button> { btnDeepSeek, btnChatGPT, btnPPTAssistant, btnXiaoHuanXiong, btnDigitalHuman, btnTextToImage, btnTextToVideo, btnTextToVoice, btnTextToPPT }
                    },
                    {
                        "SystemTools",
                        new List<AntdUI.Button> { btnReleaseMemory, btnChangePassword, btnIPInfo, btnActivationTool }
                    },
                    {
                        "NetworkTools",
                        new List<AntdUI.Button> { btnNvtOa, btnBaidu, btnFileConvert }
                    },
                    {
                        "UtilityTools",
                        new List<AntdUI.Button> { btnSearchFiles, btnMindMap, btnPrivacyLock }
                    },
                    // 使用文本方式（向后兼容）
                    {
                        "🤖 AI工具",
                        new List<AntdUI.Button> { btnDeepSeek, btnChatGPT, btnPPTAssistant, btnXiaoHuanXiong, btnDigitalHuman, btnTextToImage, btnTextToVideo, btnTextToVoice, btnTextToPPT }
                    },
                    {
                        "⚙️ 系统工具",
                        new List<AntdUI.Button> { btnReleaseMemory, btnChangePassword, btnIPInfo, btnActivationTool }
                    },
                    {
                        "🌍 网络工具",
                        new List<AntdUI.Button> { btnNvtOa, btnBaidu, btnFileConvert }
                    },
                    {
                        "🛠️ 实用工具",
                        new List<AntdUI.Button> { btnSearchFiles, btnMindMap, btnPrivacyLock }
                    }
                };

                if (categoryButtonMapping.ContainsKey(categoryName))
                {
                    System.Diagnostics.Debug.WriteLine($"找到分类映射: {categoryName}");

                    // 获取当前分类的按钮列表
                    var categoryButtons = categoryButtonMapping[categoryName];
                    System.Diagnostics.Debug.WriteLine($"该分类包含 {categoryButtons.Count} 个按钮");

                    // 首先隐藏所有按钮
                    foreach (Control control in tableLayoutPanel1.Controls)
                    {
                        if (control is AntdUI.Button)
                        {
                            control.Visible = false;
                        }
                    }

                    // 重新排列并显示分类按钮
                    RearrangeButtons(categoryButtons);
                    System.Diagnostics.Debug.WriteLine("按钮重新排列完成");
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine($"未找到分类映射: {categoryName}，显示所有按钮");
                    // 如果分类名称不匹配，显示所有按钮
                    ShowAllButtons();
                }
            }
            finally
            {
                // 恢复布局更新
                tableLayoutPanel1.ResumeLayout(true);
            }
        }

        /// <summary>
        /// 重新排列按钮位置，使其紧凑显示
        /// </summary>
        /// <param name="buttonsToShow">要显示的按钮列表</param>
        private void RearrangeButtons(List<AntdUI.Button> buttonsToShow)
        {
            int columns = tableLayoutPanel1.ColumnCount; // 4列
            int currentIndex = 0;

            foreach (var button in buttonsToShow)
            {
                if (button != null)
                {
                    // 计算新的行列位置
                    int row = currentIndex / columns;
                    int col = currentIndex % columns;

                    // 从原位置移除按钮
                    tableLayoutPanel1.Controls.Remove(button);

                    // 添加到新位置并设置为可见
                    tableLayoutPanel1.Controls.Add(button, col, row);
                    button.Visible = true;

                    currentIndex++;
                }
            }
        }

        /// <summary>
        /// 显示所有按钮并恢复原始布局
        /// </summary>
        private void ShowAllButtons()
        {
            // 暂停布局更新以提高性能
            tableLayoutPanel1.SuspendLayout();

            try
            {
                // 清空所有控件
                tableLayoutPanel1.Controls.Clear();

                // 按原始顺序重新添加所有按钮
                tableLayoutPanel1.Controls.Add(btnReleaseMemory, 0, 0);
                tableLayoutPanel1.Controls.Add(btnChangePassword, 1, 0);
                tableLayoutPanel1.Controls.Add(btnNvtOa, 2, 0);
                tableLayoutPanel1.Controls.Add(btnDeepSeek, 3, 0);
                tableLayoutPanel1.Controls.Add(btnSearchFiles, 0, 1);
                tableLayoutPanel1.Controls.Add(btnPrivacyLock, 1, 1);
                tableLayoutPanel1.Controls.Add(btnMindMap, 2, 1);
                tableLayoutPanel1.Controls.Add(btnChatGPT, 3, 1);
                tableLayoutPanel1.Controls.Add(btnFileConvert, 0, 2);
                tableLayoutPanel1.Controls.Add(btnActivationTool, 1, 2);
                tableLayoutPanel1.Controls.Add(btnBaidu, 2, 2);
                tableLayoutPanel1.Controls.Add(btnIPInfo, 3, 2);
                tableLayoutPanel1.Controls.Add(btnPPTAssistant, 0, 3);
                tableLayoutPanel1.Controls.Add(btnXiaoHuanXiong, 1, 3);
                tableLayoutPanel1.Controls.Add(btnDigitalHuman, 2, 3);
                tableLayoutPanel1.Controls.Add(btnTextToImage, 3, 3);
                tableLayoutPanel1.Controls.Add(btnTextToVideo, 0, 4);
                tableLayoutPanel1.Controls.Add(btnTextToVoice, 1, 4);
                tableLayoutPanel1.Controls.Add(btnTextToPPT, 2, 4);

                // 设置所有按钮为可见
                foreach (Control control in tableLayoutPanel1.Controls)
                {
                    if (control is AntdUI.Button)
                    {
                        control.Visible = true;
                    }
                }
            }
            finally
            {
                // 恢复布局更新
                tableLayoutPanel1.ResumeLayout(true);
            }
        }

        /// <summary>
        /// 检查并修复 Emoji 显示问题
        /// </summary>
        private void CheckAndFixEmojiDisplay()
        {
            try
            {
                // 检查系统是否支持 Emoji 显示
                bool emojiSupported = IsFontSupported("Segoe UI Emoji") || IsFontSupported("Microsoft YaHei UI");

                if (!emojiSupported)
                {
                    // 如果不支持 Emoji，使用文本替代方案
                    ReplaceEmojiWithText();
                    System.Diagnostics.Debug.WriteLine("系统不支持 Emoji 字体，已切换到文本替代方案");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"检查 Emoji 支持时发生错误: {ex.Message}");
            }
        }

        /// <summary>
        /// 检查指定字体是否可用
        /// </summary>
        /// <param name="fontName">字体名称</param>
        /// <returns>字体是否可用</returns>
        private bool IsFontSupported(string fontName)
        {
            try
            {
                using (Font testFont = new Font(fontName, 9.5f))
                {
                    return testFont.Name.Equals(fontName, StringComparison.OrdinalIgnoreCase);
                }
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// 将 Emoji 替换为文本符号
        /// </summary>
        private void ReplaceEmojiWithText()
        {
            try
            {
                foreach (TreeNode node in toolsTreeView.Nodes)
                {
                    ReplaceNodeEmojiWithText(node);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"替换 Emoji 时发生错误: {ex.Message}");
            }
        }

        /// <summary>
        /// 递归替换节点中的 Emoji
        /// </summary>
        /// <param name="node">要处理的节点</param>
        private void ReplaceNodeEmojiWithText(TreeNode node)
        {
            // Emoji 到文本的映射
            var emojiToText = new Dictionary<string, string>
            {
                { "📋 显示全部", "[*] 显示全部" },
                { "🤖 AI工具", "[AI] AI工具" },
                { "⚙️ 系统工具", "[SYS] 系统工具" },
                { "🌍 网络工具", "[NET] 网络工具" },
                { "🛠️ 实用工具", "[TOOL] 实用工具" },
                { "  🧠 DeepSeek", "  • DeepSeek" },
                { "  💬 ChatGPT", "  • ChatGPT" },
                { "  📊 PPT助手", "  • PPT助手" },
                { "  🧹 释放系统内存", "  • 释放系统内存" },
                { "  🔐 修改计算机密码", "  • 修改计算机密码" },
                { "  🌐 IP信息查看", "  • IP信息查看" },
                { "  🔑 激活工具", "  • 激活工具" },
                { "  🏢 NVT-OA", "  • NVT-OA" },
                { "  🔍 百度", "  • 百度" },
                { "  🔄 文件转换", "  • 文件转换" },
                { "  📁 搜索文件", "  • 搜索文件" },
                { "  🗺️ 思维导图", "  • 思维导图" },
                { "  📈 FineBI商业智能", "  • FineBI商业智能" }
            };

            // 替换当前节点的文本
            if (emojiToText.ContainsKey(node.Text))
            {
                node.Text = emojiToText[node.Text];
            }

            // 递归处理子节点
            foreach (TreeNode childNode in node.Nodes)
            {
                ReplaceNodeEmojiWithText(childNode);
            }
        }
    }
}
